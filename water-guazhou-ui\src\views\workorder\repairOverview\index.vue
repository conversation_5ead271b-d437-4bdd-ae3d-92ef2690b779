<!-- 维修总览 -->
<template>
  <div class="wrapper">
    <!-- 柱状图放在最顶部 -->
    <SLCard class="card" title="维修统计" overlay>
      <div class="chart-header">
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-color" style="background-color: #5B9BD5;"></span>
            <span class="legend-text">水表问题</span>
            <span class="legend-value">{{ state.summary.waterSupplyTotal }}</span>
          </div>
          <div class="legend-item">
            <span class="legend-color" style="background-color: #70AD47;"></span>
            <span class="legend-text">热表设备</span>
            <span class="legend-value">{{ state.summary.heatSupplyTotal }}</span>
          </div>
          <div class="legend-item">
            <span class="legend-color" style="background-color: #FFC000;"></span>
            <span class="legend-text">管网维修</span>
            <span class="legend-value">{{ state.summary.pipelineTotal }}</span>
          </div>
          <div class="legend-item">
            <span class="legend-color" style="background-color: #264478;"></span>
            <span class="legend-text">阀门维修</span>
            <span class="legend-value">{{ state.summary.valveTotal }}</span>
          </div>
        </div>
      </div>
      <VChart ref="refBarChart" :option="state.barOption" style="height: 400px; width: 100%;"></VChart>
    </SLCard>

    <!-- 查询条件放在中间 -->
    <SLCard class="card" title=" " overlay>
      <template #title>
        <Search ref="refSearch" :config="SearchConfig"></Search>
      </template>
    </SLCard>

    <!-- 维修记录表格 -->
    <SLCard class="card table-card" title="维修记录" overlay>
      <FormTable :config="TableConfig"></FormTable>
    </SLCard>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { IECharts } from '@/plugins/echart'
import { RepairOverviewBarOption } from './echart'
import { GetRepairOverviewStatistics, GetRepairOverviewPage, type RepairOverviewStatistics } from '@/api/workorder/repairOverview'
import moment from 'moment'

// 时间格式化
const formatterDate = 'YYYY-MM-DD'

const refSearch = ref<any>()
const refBarChart = ref<IECharts>()

// 重置表单
const resetForm = () => {
  refSearch.value?.resetForm()
  refreshTableData()
}

// 导出数据处理函数
const handleExport = () => {
  console.log('导出维修总览数据')
  // TODO: 实现数据导出功能
}

// 搜索配置
const SearchConfig = reactive({
  filters: [
    {
      field: 'date',
      label: '创建时间',
      type: 'daterange'
    },

    {
      field: 'status',
      label: '状态',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: '待审核', value: 'PENDING_REVIEW' },
        { label: '待接单', value: 'APPROVED' },
        { label: '已分配', value: 'ASSIGNED' },
        { label: '已拒绝', value: 'REJECTED' },
        { label: '已完成', value: 'COMPLETED' }
      ]
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          type: 'primary',
          text: '查询',
          icon: 'iconfont icon-chaxun',
          click: () => refreshTableData()
        },
        {
          perm: true,
          type: 'default',
          text: '重置',
          click: () => resetForm()
        }
      ]
    }
  ],
  handleSearch: () => refreshTableData(),
  defaultParams: {
    date: [
      moment().subtract(7, 'day').format(formatterDate),
      moment().format(formatterDate)
    ]
  }
} as any)

// 状态管理
const state = reactive<{
  barOption: any
  summary: {
    waterSupplyTotal: number
    heatSupplyTotal: number
    pipelineTotal: number
    valveTotal: number
  }
}>({
  barOption: null,
  summary: {
    waterSupplyTotal: 0,
    heatSupplyTotal: 0,
    pipelineTotal: 0,
    valveTotal: 0
  }
})



// 分页变更处理函数
const handlePageChange = (page: number, size: number) => {
  TableConfig.pagination.page = page
  TableConfig.pagination.limit = size
  refreshTableData()
}

// 表格配置
const TableConfig = reactive({
  columns: [
    { label: '维修事件', prop: 'title', minWidth: 150 },
    { label: '上报地址', prop: 'address', minWidth: 200 },
    { label: '维修类型', prop: 'typeName', minWidth: 120 },
    {
      label: '维修状态',
      prop: 'status',
      minWidth: 100,
      formatter: (row: any) => {
        const statusMap: Record<string, string> = {
          'PENDING_REVIEW': '待审核',
          'APPROVED': '待接单',
          'ASSIGNED': '已分配',
          'REJECTED': '已拒绝',
          'COMPLETED': '已完成'
        }
        return statusMap[row.status] || row.status
      }
    },
    {
      label: '创建时间',
      prop: 'createTime',
      minWidth: 180,
      formatter: (row: any) => row.createTime || '-'
    },
    { label: '创建人', prop: 'organizerName', minWidth: 100 },
    { label: '备注', prop: 'remark', minWidth: 150 }
  ],
  dataList: [] as any[],
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    change: handlePageChange
  }
})

// 刷新柱状图数据（不受查询条件影响）
const refreshChartData = async () => {
  try {
    // 使用固定的时间范围获取图表数据（比如最近一年）
    const params = {
      fromTime: moment().subtract(1, 'year').valueOf(),
      toTime: moment().valueOf()
    }

    const res = await GetRepairOverviewStatistics(params as any)
    const data: RepairOverviewStatistics = res.data?.data || {}

    // 调试：打印月度数据
    console.log('月度数据:', data.monthlyData)

    // 更新图表
    state.barOption = RepairOverviewBarOption(data.monthlyData || [])
    state.summary = data.summary || state.summary
  } catch (error) {
    console.error('获取维修图表数据失败:', error)
    // 设置空数据
    state.barOption = RepairOverviewBarOption([])
    state.summary = { waterSupplyTotal: 0, heatSupplyTotal: 0, pipelineTotal: 0, valveTotal: 0 }
  }
}

// 刷新表格数据（受查询条件影响）
const refreshTableData = async () => {
  try {
    const query = refSearch.value?.queryParams || {}

    // 处理创建时间范围，转换为时间戳
    const [fromTime, toTime] = query.date?.length === 2
      ? [
          moment(query.date[0]).valueOf(),
          moment(query.date[1]).valueOf()
        ]
      : [
          moment().subtract(7, 'days').valueOf(),
          moment().valueOf()
        ]

    const params = {
      fromTime,
      toTime,
      title: query.title,
      type: query.type,
      status: query.status,
      page: TableConfig.pagination.page,
      size: TableConfig.pagination.limit
    }

    const res = await GetRepairOverviewPage(params as any)
    const responseData = res.data?.data || {}

    // 更新表格数据 - 根据实际API返回结构解析
    // API返回结构：{ total: 3, data: [...] }
    TableConfig.dataList = responseData.data || []
    TableConfig.pagination.total = responseData.total || 0
  } catch (error) {
    console.error('获取维修记录数据失败:', error)
    // 设置空数据
    TableConfig.dataList = []
    TableConfig.pagination.total = 0
  }
}

onMounted(() => {
  // 初始化时加载图表数据和表格数据
  refreshChartData()
  refreshTableData()
})
</script>

<style lang="scss" scoped>
.wrapper {
  padding: 20px;
  background-color: #f5f5f5;
}

.card {
  margin-bottom: 20px;
}



.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.chart-legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-text {
  font-size: 12px;
  color: #666;
}

.legend-value {
  font-size: 12px;
  font-weight: bold;
  color: #333;
}

.table-card {
  :deep(.sl-card__body) {
    padding: 20px;
  }

  :deep(.el-table) {
    width: 100% !important;
  }

  :deep(.el-table__body-wrapper) {
    width: 100% !important;
  }

  :deep(.el-table th) {
    text-align: center;
  }

  :deep(.el-table td) {
    text-align: center;
  }
}
</style>
