package org.thingsboard.server.dao.orderWork;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.thingsboard.server.dao.mapper.workOrder.EventOverviewMapper;
import org.thingsboard.server.dao.model.sql.workOrder.EventOverview;
import org.thingsboard.server.dao.model.sql.workOrder.EventStatusEnums;
import org.thingsboard.server.dao.util.imodel.query.workOrder.RepairOverviewPageRequest;

import java.util.*;

/**
 * 维修总览服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class RepairOverviewServiceImpl implements RepairOverviewService {

    @Autowired
    private EventOverviewMapper eventOverviewMapper;

    @Override
    public IPage<EventOverview> findRepairOverviewByPage(RepairOverviewPageRequest request) {
        Page<EventOverview> page = new Page<>(request.getPage(), request.getSize());
        QueryWrapper<EventOverview> queryWrapper = buildQueryWrapper(request);
        
        // 设置排序
        if (StringUtils.hasText(request.getOrderBy())) {
            String orderBy = convertOrderByField(request.getOrderBy());
            if ("DESC".equalsIgnoreCase(request.getOrderDirection())) {
                queryWrapper.orderByDesc(orderBy);
            } else {
                queryWrapper.orderByAsc(orderBy);
            }
        } else {
            queryWrapper.orderByDesc("create_time");
        }

        return eventOverviewMapper.selectPage(page, queryWrapper);
    }

    @Override
    public Map<String, Object> getRepairStatistics(RepairOverviewPageRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取月度统计数据
            List<Map<String, Object>> monthlyData = getMonthlyStatistics(request);
            result.put("monthlyData", monthlyData);
            
            // 获取汇总统计
            Map<String, Object> summary = getSummaryStatistics(request);
            result.put("summary", summary);
            
            // 获取维修记录（前10条）
            RepairOverviewPageRequest recordRequest = new RepairOverviewPageRequest();
            recordRequest.setTenantId(request.getTenantId());
            recordRequest.setFromTime(String.valueOf(request.getFromTime().getTime()));
            recordRequest.setToTime(String.valueOf(request.getToTime().getTime()));
            // 只有当到期时间不为null时才设置，避免空字符串
            if (request.getExpireFromTime() != null) {
                recordRequest.setExpireFromTime(String.valueOf(request.getExpireFromTime().getTime()));
            }
            if (request.getExpireToTime() != null) {
                recordRequest.setExpireToTime(String.valueOf(request.getExpireToTime().getTime()));
            }
            recordRequest.setStatus(request.getStatus());
            recordRequest.setPage(1);
            recordRequest.setSize(10);
            
            IPage<EventOverview> recordsResult = findRepairOverviewByPage(recordRequest);
            
            // 转换记录格式
            List<Map<String, Object>> records = new ArrayList<>();
            for (EventOverview event : recordsResult.getRecords()) {
                Map<String, Object> record = new HashMap<>();
                record.put("id", event.getId());
                record.put("deviceName", event.getTitle() != null ? event.getTitle() : "维修事件");
                record.put("location", event.getAddress() != null ? event.getAddress() : "未知地址");
                record.put("repairType", event.getTypeName() != null ? event.getTypeName() : getRepairTypeByEventType(event.getType()));
                record.put("repairStatus", convertEventStatusToString(event.getStatus()));
                record.put("createTime", event.getCreateTime());
                // 创建人字段会通过@ParseUsername注解自动转换为用户名
                record.put("creator", event.getOrganizerId() != null ? event.getOrganizerId() : "系统");
                record.put("duration", calculateDuration(event.getCreateTime(), event.getUpdateTime()));
                records.add(record);
            }
            
            result.put("repairRecords", records);
            
        } catch (Exception e) {
            log.error("Failed to get repair statistics", e);
            // 返回空数据
            result.put("monthlyData", new ArrayList<>());
            result.put("summary", new HashMap<>());
            result.put("repairRecords", new ArrayList<>());
        }
        
        return result;
    }

    @Override
    public List<Map<String, Object>> getMonthlyStatistics(RepairOverviewPageRequest request) {
        List<Map<String, Object>> monthlyData = new ArrayList<>();

        try {
            QueryWrapper<EventOverview> queryWrapper = buildQueryWrapper(request);
            queryWrapper.orderByAsc("create_time");

            // 查询事件数据
            List<EventOverview> events = eventOverviewMapper.selectList(queryWrapper);

            // 根据查询时间范围确定需要显示的月份
            Calendar startCal = Calendar.getInstance();
            Calendar endCal = Calendar.getInstance();

            if (request.getFromTime() != null && request.getToTime() != null) {
                startCal.setTime(request.getFromTime());
                endCal.setTime(request.getToTime());
            } else {
                // 如果没有指定时间范围，默认显示当前年份的所有月份
                startCal.set(Calendar.MONTH, 0); // 1月
                startCal.set(Calendar.DAY_OF_MONTH, 1);
                endCal.set(Calendar.MONTH, 11); // 12月
                endCal.set(Calendar.DAY_OF_MONTH, 31);
            }

            // 生成需要显示的月份列表
            List<String> monthsToShow = new ArrayList<>();
            Map<String, Map<String, Integer>> monthlyStats = new HashMap<>();

            Calendar tempCal = (Calendar) startCal.clone();
            while (tempCal.compareTo(endCal) <= 0) {
                int year = tempCal.get(Calendar.YEAR);
                int month = tempCal.get(Calendar.MONTH) + 1;
                String monthKey = year + "-" + String.format("%02d", month);
                String monthDisplay = year + "年" + month + "月";

                monthsToShow.add(monthKey);

                Map<String, Integer> stats = new HashMap<>();
                stats.put("waterSupplyIssues", 0);
                stats.put("heatSupplyIssues", 0);
                stats.put("pipelineIssues", 0);
                stats.put("valveIssues", 0);
                monthlyStats.put(monthKey, stats);

                // 移动到下一个月
                tempCal.add(Calendar.MONTH, 1);
            }

            // 统计每个月的数据
            Calendar calendar = Calendar.getInstance();
            for (EventOverview event : events) {
                if (event.getCreateTime() != null) {
                    calendar.setTime(event.getCreateTime());
                    int year = calendar.get(Calendar.YEAR);
                    int month = calendar.get(Calendar.MONTH) + 1;
                    String monthKey = year + "-" + String.format("%02d", month);

                    String category = categorizeEvent(event);
                    Map<String, Integer> monthStats = monthlyStats.get(monthKey);
                    if (monthStats != null) {
                        monthStats.put(category, monthStats.get(category) + 1);
                    }
                }
            }

            // 转换为前端需要的格式
            for (String monthKey : monthsToShow) {
                String[] parts = monthKey.split("-");
                int year = Integer.parseInt(parts[0]);
                int month = Integer.parseInt(parts[1]);
                String monthDisplay = year + "年" + month + "月";

                Map<String, Object> monthData = new HashMap<>();
                monthData.put("month", monthDisplay);
                monthData.put("waterSupplyIssues", monthlyStats.get(monthKey).get("waterSupplyIssues"));
                monthData.put("heatSupplyIssues", monthlyStats.get(monthKey).get("heatSupplyIssues"));
                monthData.put("pipelineMaintenance", monthlyStats.get(monthKey).get("pipelineIssues"));
                monthData.put("valveMaintenance", monthlyStats.get(monthKey).get("valveIssues"));
                monthlyData.add(monthData);
            }

        } catch (Exception e) {
            log.error("Failed to get monthly statistics", e);
            // 返回空数据，至少显示当前月份
            Calendar now = Calendar.getInstance();
            int year = now.get(Calendar.YEAR);
            int month = now.get(Calendar.MONTH) + 1;
            String monthDisplay = year + "年" + month + "月";

            Map<String, Object> monthData = new HashMap<>();
            monthData.put("month", monthDisplay);
            monthData.put("waterSupplyIssues", 0);
            monthData.put("heatSupplyIssues", 0);
            monthData.put("pipelineMaintenance", 0);
            monthData.put("valveMaintenance", 0);
            monthlyData.add(monthData);
        }

        return monthlyData;
    }

    @Override
    public Map<String, Object> getSummaryStatistics(RepairOverviewPageRequest request) {
        Map<String, Object> summary = new HashMap<>();

        try {
            QueryWrapper<EventOverview> queryWrapper = buildQueryWrapper(request);

            // 查询事件数据
            List<EventOverview> events = eventOverviewMapper.selectList(queryWrapper);

            // 统计各类型数量
            int waterSupplyTotal = 0;
            int heatSupplyTotal = 0;
            int pipelineTotal = 0;
            int valveTotal = 0;

            for (EventOverview event : events) {
                String category = categorizeEvent(event);
                switch (category) {
                    case "waterSupplyIssues":
                        waterSupplyTotal++;
                        break;
                    case "heatSupplyIssues":
                        heatSupplyTotal++;
                        break;
                    case "pipelineIssues":
                        pipelineTotal++;
                        break;
                    case "valveIssues":
                        valveTotal++;
                        break;
                }
            }

            summary.put("waterSupplyTotal", waterSupplyTotal);
            summary.put("heatSupplyTotal", heatSupplyTotal);
            summary.put("pipelineTotal", pipelineTotal);
            summary.put("valveTotal", valveTotal);
            summary.put("totalEvents", events.size());

        } catch (Exception e) {
            log.error("Failed to get summary statistics", e);
            summary.put("waterSupplyTotal", 0);
            summary.put("heatSupplyTotal", 0);
            summary.put("pipelineTotal", 0);
            summary.put("valveTotal", 0);
            summary.put("totalEvents", 0);
        }

        return summary;
    }

    @Override
    public List<EventOverview> exportRepairOverview(RepairOverviewPageRequest request) {
        QueryWrapper<EventOverview> queryWrapper = buildQueryWrapper(request);
        
        // 设置排序
        if (StringUtils.hasText(request.getOrderBy())) {
            String orderBy = convertOrderByField(request.getOrderBy());
            if ("DESC".equalsIgnoreCase(request.getOrderDirection())) {
                queryWrapper.orderByDesc(orderBy);
            } else {
                queryWrapper.orderByAsc(orderBy);
            }
        } else {
            queryWrapper.orderByDesc("create_time");
        }
        
        return eventOverviewMapper.selectList(queryWrapper);
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<EventOverview> buildQueryWrapper(RepairOverviewPageRequest request) {
        QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();
        
        // 租户过滤
        if (StringUtils.hasText(request.getTenantId())) {
            queryWrapper.eq("tenant_id", request.getTenantId());
        }
        
        // 创建时间范围过滤（使用继承的时间处理）
        if (request.getFromTime() != null) {
            queryWrapper.ge("create_time", request.getFromTime());
        }
        if (request.getToTime() != null) {
            queryWrapper.le("create_time", request.getToTime());
        }

        // 到期时间范围过滤
        if (request.getExpireFromTime() != null) {
            queryWrapper.ge("expire_time", request.getExpireFromTime());
        }
        if (request.getExpireToTime() != null) {
            queryWrapper.le("expire_time", request.getExpireToTime());
        }

        // 状态过滤
        if (StringUtils.hasText(request.getStatus())) {
            queryWrapper.eq("status", convertStatusToEnum(request.getStatus()));
        }
        
        return queryWrapper;
    }

    /**
     * 转换排序字段
     */
    private String convertOrderByField(String orderBy) {
        // 转换前端字段名到数据库字段名
        switch (orderBy) {
            case "createTime":
                return "create_time";
            case "updateTime":
                return "update_time";
            default:
                return orderBy;
        }
    }

    /**
     * 将状态字符串转换为枚举
     */
    private EventStatusEnums convertStatusToEnum(String status) {
        if (status == null || status.trim().isEmpty()) {
            return null;
        }

        switch (status.trim()) {
            case "待审核":
                return EventStatusEnums.PENDING_REVIEW;
            case "待接单":
                return EventStatusEnums.ASSIGNED;
            case "已驳回":
                return EventStatusEnums.REJECTED;
            case "已完成":
                return EventStatusEnums.COMPLETED;
            case "处理中":
                return EventStatusEnums.PROCESSING;
            default:
                return EventStatusEnums.PENDING_REVIEW;
        }
    }

    /**
     * 将事件状态枚举转换为字符串
     */
    private String convertEventStatusToString(EventStatusEnums status) {
        if (status == null) {
            return "未知";
        }

        switch (status) {
            case PENDING_REVIEW:
                return "待审核";
            case ASSIGNED:
                return "待接单";
            case REJECTED:
                return "已驳回";
            case COMPLETED:
                return "已完成";
            case PROCESSING:
                return "处理中";
            default:
                return "未知";
        }
    }

    /**
     * 根据事件类型获取维修类型
     */
    private String getRepairTypeByEventType(String eventType) {
        if (eventType == null || eventType.trim().isEmpty()) {
            return "其他";
        }

        String type = eventType.toLowerCase();
        if (type.contains("供水") || type.contains("water")) {
            return "供水问题";
        } else if (type.contains("供热") || type.contains("heat")) {
            return "供热问题";
        } else if (type.contains("管道") || type.contains("pipeline")) {
            return "管道问题";
        } else if (type.contains("阀门") || type.contains("valve")) {
            return "阀门问题";
        } else {
            return "其他";
        }
    }

    /**
     * 对事件进行分类
     */
    private String categorizeEvent(EventOverview event) {
        String type = event.getType();
        String typeName = event.getTypeName();

        // 优先使用typeName进行分类
        if (typeName != null && !typeName.trim().isEmpty()) {
            String name = typeName.toLowerCase();
            if (name.contains("供水") || name.contains("water")) {
                return "waterSupplyIssues";
            } else if (name.contains("供热") || name.contains("heat")) {
                return "heatSupplyIssues";
            } else if (name.contains("管道") || name.contains("pipeline")) {
                return "pipelineIssues";
            } else if (name.contains("阀门") || name.contains("valve")) {
                return "valveIssues";
            }
        }

        // 使用type进行分类
        if (type != null && !type.trim().isEmpty()) {
            String typeStr = type.toLowerCase();
            if (typeStr.contains("供水") || typeStr.contains("water")) {
                return "waterSupplyIssues";
            } else if (typeStr.contains("供热") || typeStr.contains("heat")) {
                return "heatSupplyIssues";
            } else if (typeStr.contains("管道") || typeStr.contains("pipeline")) {
                return "pipelineIssues";
            } else if (typeStr.contains("阀门") || typeStr.contains("valve")) {
                return "valveIssues";
            }
        }

        // 默认分类为供水问题
        return "waterSupplyIssues";
    }

    /**
     * 计算持续时间
     */
    private String calculateDuration(Date createTime, Date updateTime) {
        if (createTime == null) {
            return "未知";
        }

        Date endTime = updateTime != null ? updateTime : new Date();
        long diffInMillis = endTime.getTime() - createTime.getTime();
        long diffInHours = diffInMillis / (1000 * 60 * 60);

        if (diffInHours < 1) {
            long diffInMinutes = diffInMillis / (1000 * 60);
            return diffInMinutes + "分钟";
        } else if (diffInHours < 24) {
            return diffInHours + "小时";
        } else {
            long diffInDays = diffInHours / 24;
            return diffInDays + "天";
        }
    }
}
