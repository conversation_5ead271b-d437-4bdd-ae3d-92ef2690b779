import * as echarts from 'echarts'
import { useAppStore } from '@/store'

/**
 * 维修总览堆叠柱状图配置
 */
export const RepairOverviewBarOption = (data: Array<{
  month: string
  waterSupplyIssues: number
  heatSupplyIssues: number
  pipelineMaintenance: number
  valveMaintenance: number
}>) => {
  // 调试：打印接收到的数据
  console.log('图表数据:', data)

  const months = data.map(item => item.month)
  const waterSupplyData = data.map(item => item.waterSupplyIssues)
  const heatSupplyData = data.map(item => item.heatSupplyIssues)
  const pipelineData = data.map(item => item.pipelineMaintenance)
  const valveData = data.map(item => item.valveMaintenance)

  // 调试：打印处理后的数据
  console.log('月份:', months)
  console.log('水表问题:', waterSupplyData)
  console.log('热表设备:', heatSupplyData)
  console.log('管网维修:', pipelineData)
  console.log('阀门维修:', valveData)

  return {
    backgroundColor: useAppStore().isDark ? '#131624' : '#F4F7FA',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params: any) {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['水表问题', '热表设备', '管网维修', '阀门维修'],
      top: 10,
      right: 20,
      textStyle: {
        color: useAppStore().isDark ? '#fff' : '#666',
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: months,
        axisTick: {
          alignWithLabel: true
        },
        axisLabel: {
          color: useAppStore().isDark ? '#fff' : '#666',
          fontSize: 12,
          rotate: months.length > 6 ? 45 : 0, // 当月份超过6个时旋转标签
          interval: 0 // 显示所有标签
        },
        axisLine: {
          lineStyle: {
            color: useAppStore().isDark ? '#444' : '#ddd'
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        minInterval: 1, // 设置最小间隔为1，确保只显示整数
        axisLabel: {
          color: useAppStore().isDark ? '#fff' : '#666',
          fontSize: 12,
          formatter: '{value}' // 确保显示整数
        },
        axisLine: {
          lineStyle: {
            color: useAppStore().isDark ? '#444' : '#ddd'
          }
        },
        splitLine: {
          lineStyle: {
            color: useAppStore().isDark ? '#333' : '#eee'
          }
        }
      }
    ],
    series: [
      {
        name: '水表问题',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        data: waterSupplyData,
        itemStyle: {
          color: '#5B9BD5'
        }
      },
      {
        name: '热表设备',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        data: heatSupplyData,
        itemStyle: {
          color: '#70AD47'
        }
      },
      {
        name: '管网维修',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        data: pipelineData,
        itemStyle: {
          color: '#FFC000'
        }
      },
      {
        name: '阀门维修',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        data: valveData,
        itemStyle: {
          color: '#264478'
        }
      }
    ]
  }
}

// /**
//  * 维修类型饼图配置
//  */
// export const RepairTypePieOption = (summary: {
//   waterSupplyTotal: number
//   heatSupplyTotal: number
//   pipelineTotal: number
//   valveTotal: number
// }) => {
//   const data = [
//     { value: summary.waterSupplyTotal, name: '水表问题', itemStyle: { color: '#5B9BD5' } },
//     { value: summary.heatSupplyTotal, name: '热表设备', itemStyle: { color: '#70AD47' } },
//     { value: summary.pipelineTotal, name: '管网维修', itemStyle: { color: '#FFC000' } },
//     { value: summary.valveTotal, name: '阀门维修', itemStyle: { color: '#264478' } }
//   ]

//   return {
//     backgroundColor: useAppStore().isDark ? '#131624' : '#F4F7FA',
//     tooltip: {
//       trigger: 'item',
//       formatter: '{a} <br/>{b}: {c} ({d}%)'
//     },
//     legend: {
//       orient: 'vertical',
//       right: 10,
//       top: 'center',
//       textStyle: {
//         color: useAppStore().isDark ? '#fff' : '#666',
//         fontSize: 12
//       }
//     },
//     series: [
//       {
//         name: '维修类型分布',
//         type: 'pie',
//         radius: ['40%', '70%'],
//         center: ['40%', '50%'],
//         avoidLabelOverlap: false,
//         label: {
//           show: false,
//           position: 'center'
//         },
//         emphasis: {
//           label: {
//             show: true,
//             fontSize: '18',
//             fontWeight: 'bold'
//           }
//         },
//         labelLine: {
//           show: false
//         },
//         data: data
//       }
//     ]
//   }
// }
